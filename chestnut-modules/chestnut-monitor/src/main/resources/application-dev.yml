# 项目相关配置
chestnut:
  # 名称
  name: Chestnut-Monitor

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为9090
  port: 9090
  # 开启优雅停机
  shutdown: graceful 
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.chestnut: debug
    org.springframework: warn

spring:
  security:
    user:
      name: chestnut
      password: 123456
  boot:
    admin:
      server:
        enabled: true
      ui:
        title: 服务监控中心
      context-path: /admin
