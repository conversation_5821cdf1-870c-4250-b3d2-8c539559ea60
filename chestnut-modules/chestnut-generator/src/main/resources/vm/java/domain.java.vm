package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * ${functionName}对象 ${tableName}
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Getter
@Setter
@TableName(value = ${ClassName}.TABLE_NAME)
public class ${ClassName} implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "${tableName}";

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
#elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd")
#end
#end
    private $column.javaType $column.javaField;

#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    private List<${subClassName}> ${subclassName}List;

#end

#if($table.sub)
    public List<${subClassName}> get${subClassName}List()
    {
        return ${subclassName}List;
    }

    public void set${subClassName}List(List<${subClassName}> ${subclassName}List)
    {
        this.${subclassName}List = ${subclassName}List;
    }
#end
}
