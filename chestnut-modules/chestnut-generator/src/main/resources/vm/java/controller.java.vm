package ${packageName}.controller;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chestnut.common.security.web.PageRequest;
import com.chestnut.common.utils.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.chestnut.common.security.web.BaseRestController;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.chestnut.common.security.anno.Priv;
import com.chestnut.system.security.AdminUserType;
import lombok.RequiredArgsConstructor;
import com.chestnut.common.domain.R;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseRestController {

    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @Priv
    @GetMapping("/list")
#if($table.crud || $table.sub)
    public R<?> list(${ClassName} ${className})
    {
        PageRequest pr = this.getPageRequest();
        LambdaQueryChainWrapper<CcProducts> wrapper = this.ccProductsService.lambdaQuery();
        #foreach($column in $columns)
            #set($queryType=$column.queryType)
            #set($javaField=$column.javaField)
            #set($javaType=$column.javaType)
            #set($columnName=$column.columnName)
            #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #if($column.query)
                #if($column.queryType == "EQ")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.eq(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "NE")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.ne(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "GT")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.gt(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "GTE")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.gte(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "LT")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.lt(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "LTE")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.lte(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #elseif($queryType == "LIKE")
                    if (${className}.get${AttrName}() != null) {
                        wrapper.like(${ClassName}::get${AttrName},${className}.get${AttrName}());
                    }
                #end
            #end
        #end

        Page<CcProducts> page = wrapper
                .page(new Page<>(pr.getPageNumber(), pr.getPageSize(), true));

        return this.bindDataTable(page);
    }
#elseif($table.tree)
    public R<?> list(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return R.ok(list);
    }
#end

    /**
     * 获取${functionName}详细信息
     */
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public R<?> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return R.ok(this.${className}Service.getById(id));
    }

    /**
     * 新增${functionName}
     */
    @Priv
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@RequestBody ${ClassName} ${className})
    {
        this.${className}Service.save(${className});
        return R.ok();
    }

    /**
     * 修改${functionName}
     */
    @Priv
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@RequestBody ${ClassName} ${className})
    {
        this.${className}Service.updateById(${className});
        return R.ok();
    }

    /**
     * 删除${functionName}
     */
    @Priv
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
	@DeleteMapping("/{${pkColumn.javaField}s}")
    public R<?> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        this.${className}Service.removeBatchByIds(List.of(${pkColumn.javaField}s));
        return R.ok();
    }
}
