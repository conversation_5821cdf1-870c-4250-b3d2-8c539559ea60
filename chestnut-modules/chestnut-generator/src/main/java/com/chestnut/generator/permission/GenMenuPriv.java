/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.generator.permission;

public interface GenMenuPriv {

	public String List = "tool:gen:view";

	public String Import = "tool:gen:import";

	public String Edit = "tool:gen:edit";

	public String Remove = "tool:gen:remove";

	public String Preview = "tool:gen:preview";

	public String GenCode = "tool:gen:code";
}
