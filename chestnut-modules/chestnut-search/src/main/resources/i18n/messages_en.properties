#错误消息
ERRCODE.SEARCH.DICT_WORD_EXISTS=The dict word "{0}" already exists.
ERRCODE.SEARCH.MODEL_NOT_EXISTS=The index model "{0}" not exist.
ERRCODE.SEARCH.UNSUPPORTED_SEARCH_TYPE=Unsuppoted search type: {0}.
ERRCODE.SEARCH.ESConnectFail=Elastic search server connect failed, please check if the yaml config is correct or if the ES service is valid.

# 字典数据
DICT.SearchDictWordType=Search dict word type
DICT.SearchDictWordType.WORD=ExtWord
DICT.SearchDictWordType.STOP=StopWord
DICT.WordAnalyzeType=Word Analyze Type
DICT.WordAnalyzeType.ik_smart=IKSmart
DICT.WordAnalyzeType.ik_max_word=IKMaxWord

SCHEDULED_TASK.SearchWordStatJob=Search word hour stat update job