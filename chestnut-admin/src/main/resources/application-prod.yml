# 项目相关配置
chestnut:
  # 名称
  name: 栗子内容管理系统
  # 代号
  alias: ChestnutCMS
  # 版本
  version: 1.4.5
  # 版权年份
  copyrightYear: 2022-2024
  system:
    # 演示模式开关
    demoMode: true
    # 文件路径 示例（ Windows配置D:/chestnut/uploadPath，Linux配置 /home/<USER>/uploadPath）
    uploadPath: /home/<USER>/uploadPath
    # 验证码类型 math 数组计算 char 字符验证
    captchaType: math
  freemarker:
    templateLoaderPath: /home/<USER>/statics
  cms:
    resourceRoot: /home/<USER>/wwwroot_release
    publish:
      pool:
        threadNamePrefix: "CMS-PUBLISH-"
        queueCapacity: 10000
        coreSize: 2
        maxSize: 4

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8090
  port: 8090
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.chestnut: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart: 
      # 单个文件大小
      max-file-size:  100MB
      # 设置总上传的文件大小
      max-request-size:  100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  freemarker:
    check-template-location: false
  elasticsearch:
    uris: http://cc-elasticsearch:9200
    username: elastic
    password: hello1234
  # redis 配置
  data:
    redis:
      # 地址
      host: cc-redis
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password: b18a03
      # 连接超时时间
      timeout: 10s
      lettuce: 
        pool: 
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  flyway:
    enabled: false
    # 迁移sql脚本文件存放路径，默认：classpath:db/migration
    locations: classpath:db/migration/mysql
    # 迁移sql脚本文件名称的前缀，默认：V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称分隔符，默认2个下划线：__
    sql-migration-separator: __
    # 迁移sql脚本文件名称后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
  # 数据库配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    dynamic:
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      # 主库
      datasource:
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************************************************************
          username: root
          password: hello1234
        # 从库
        # slave:
        #  lazy: true
        #  type: ${spring.datasource.type}
        #  driverClassName: com.mysql.cj.jdbc.Driver
        #  url: ************************************************************************************************************************************************************************************************
        #  username:
        #  password:
    hikari:
      # 连接池名
      pool-name: HikariCP
      # 连接超时时间：毫秒, 默认30秒
      connection-timeout: 2000
      # 最小空闲连接，默认值10，小于0或大于maximum-pool-size，都会重置为maximum-pool-size
      minimum-idle: 5
      # 最大连接数，小于等于0会被重置为默认值10；大于零小于1会被重置为minimum-idle的值
      maximum-pool-size: 20
      # 空闲连接最大存活时间，默认值600000（10分钟），大于等于max-lifetime且max-lifetime>0，会被重置为0；不等于0且小于10秒，会被重置为10秒。
      idle-timeout: 200000
      # 连接池返回的连接默认自动提交，默认只 true
      auto-commit: true
      # 连接最大存活时间，不等于0且小于30秒，会被重置为默认值30分钟.设置应该比mysql设置的超时时间短
      max-lifetime: 1800000
      # 用于测试连接是否可用的查询语句
      connection-test-query: SELECT 1
  # 邮件配置
  mail:
    host: smtp.163.com
    port: 465
    username: <EMAIL>
    # 授权码
    password: xxx
    # 编码格式
    default-encoding: utf-8
    # 协议
    protocol: smtps
    properties:
      mail:
        smtp:
          ssl:
            enable: true
          auth: true
          starttls:
            enable: true
            required: true
  # 监控配置
  application:
    name: "ChestnutCMS"
  boot:
    admin:
      client:
        # 增加客户端开关
        enabled: false
        # Admin Server URL
        url: http://127.0.0.1:8090/admin
        instance:
          service-host-type: IP
        username: chestnut
        password: 123456

# SaToken配置           
sa-token: 
    # token名称 (同时也是cookie名称)
    token-name: Authorization
    # token前缀
    token-prefix: Bearer
    # token有效期，单位s 默认30天, -1代表永不过期 
    timeout: 2592000
    # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
    active-timeout: -1
    # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录) 
    is-concurrent: true
    # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token) 
    is-share: true
    # token风格
    token-style: uuid
    # 是否输出操作日志 
    is-log: true           

# MyBatis配置
mybatis-plus:
  global-config:
    enable-sql-runner: true
  # 搜索指定包别名
  typeAliasesPackage: com.chestnut.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  mode: clean
  # 过滤链接
  urlPatterns: 
   - /system/* 
   - /monitor/*
   - /tool/*

xxl:
  job:
    enable: false
    accessToken: default_token
    adminAddresses: http://127.0.0.1:18080/xxl-job-admin
    executor:
      ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: chestnut-admin
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      #address: 
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip: 
      ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: 9968
      ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: E:/dev/workspace_chestnut/ChestnutCMS/chestnut-modules/chestnut-xxljob/jobhandler
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；默认：30
      logretentiondays: 30

jasypt:
  encryptor:
    password: qsakjdnfij234234sdf67