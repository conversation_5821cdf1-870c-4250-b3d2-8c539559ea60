CREATE TABLE `sync_module_log` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
                                   `module_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块类型',
                                   `last_update_time` bigint DEFAULT NULL COMMENT '最后更新时间',
                                   `request_path` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求接口路径',
                                   `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='同步模块数据日志表';

ALTER TABLE `cms_site`
    ADD COLUMN `dept_id` bigint NULL COMMENT '所属部门id' AFTER `dept_code`;

ALTER TABLE `cms_catalog`
    ADD COLUMN `flow_design_id` char(50) NULL COMMENT '工作流设计id' AFTER `config_props`;

ALTER TABLE `cms_content`
    ADD COLUMN `flow_task_id` char(50) NULL COMMENT '流程id' AFTER `prop4`,
    ADD COLUMN `flow_task_state` varchar(255) NULL COMMENT '审核状态' AFTER `flow_task_id`;

ALTER TABLE `sys_dept`
    MODIFY COLUMN `ancestors` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表' AFTER `parent_id`;

ALTER TABLE `cms_content`
    ADD COLUMN `flow_design_id` char(50) NULL COMMENT '流程设计id' AFTER `prop4`;

ALTER TABLE `cms_content`
    ADD COLUMN `data_id` char(50) NULL COMMENT '关联流程数据id' AFTER `prop4`;

ALTER TABLE `cms_content`
    MODIFY COLUMN `original` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否原创（Y=是，N=否）' AFTER `source_url`;

ALTER TABLE `sys_role`
    ADD COLUMN `child_show` char(1) NULL DEFAULT '0' COMMENT '下级单位是否可见（0是 1否）' AFTER `status`,
    ADD COLUMN `dept_id` bigint NULL COMMENT '部门id' AFTER `childShow`,
    ADD INDEX `idx_dept_id`(`dept_id`);

ALTER TABLE `cms_audio`
    MODIFY COLUMN `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简介' AFTER `author`;

ALTER TABLE `cms_image`
    MODIFY COLUMN `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片摘要' AFTER `title`;

ALTER TABLE `cms_video`
    MODIFY COLUMN `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简介' AFTER `title`;

ALTER TABLE `chestnut_cms`.`sys_user`
    ADD COLUMN `inner_dept_id` bigint NULL COMMENT '院内部门id' AFTER `dept_id`,
    ADD INDEX `dept_id`(`dept_id`),
    ADD INDEX `inner_dept_id`(`inner_dept_id`),
    ADD FULLTEXT INDEX `nick_name`(`nick_name`);

ALTER TABLE `chestnut_cms`.`sys_dept`
    ADD COLUMN `type` tinyint NULL DEFAULT 1 COMMENT '部门类型：1 部门 2院内部门' AFTER `status`,
    ADD INDEX `parent_id`(`parent_id`),
    ADD INDEX `ancestors`(`ancestors`),
    ADD INDEX `type`(`type`),
    ADD FULLTEXT INDEX `dept_name`(`dept_name`);
