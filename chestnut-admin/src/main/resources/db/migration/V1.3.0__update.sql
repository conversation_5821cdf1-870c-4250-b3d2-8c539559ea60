CREATE TABLE `cms_custom_form` (
  `form_id` bigint NOT NULL COMMENT 'ID',
  `site_id` bigint NOT NULL COMMENT '站点ID ',
  `model_id` bigint NOT NULL COMMENT '关联元数据模型ID',
  `name` varchar(100) NOT NULL COMMENT '表单名称',
  `code` varchar(50) NOT NULL COMMENT '同站点唯一编码',
  `status` int NOT NULL COMMENT '状态',
  `templates` varchar(100) DEFAULT NULL COMMENT '模板',
  `need_captcha` varchar(1) NOT NULL COMMENT '是否启用验证码',
  `need_login` varchar(1) NOT NULL COMMENT '是否需要会员登录',
  `rule_limit` varchar(1) NOT NULL COMMENT '唯一性规则限制（IP/浏览器指纹）',
  `create_by` varchar(64) NOT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`form_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `cms_cfd_default` (
  `data_id` bigint NOT NULL COMMENT '数据主键ID',
  `model_id` bigint NOT NULL COMMENT '自定义表单ID（元数据模型ID）',
  `site_id` bigint NOT NULL COMMENT '所属站点ID',
  `client_ip` varchar(64) NOT NULL COMMENT 'IP',
  `uuid` varchar(128) NOT NULL COMMENT '用户唯一标识（浏览器指纹、会员ID）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `short_text1` varchar(50) DEFAULT NULL,
  `short_text2` varchar(50) DEFAULT NULL,
  `short_text3` varchar(50) DEFAULT NULL,
  `short_text4` varchar(50) DEFAULT NULL,
  `short_text5` varchar(50) DEFAULT NULL,
  `short_text6` varchar(50) DEFAULT NULL,
  `short_text7` varchar(50) DEFAULT NULL,
  `short_text8` varchar(50) DEFAULT NULL,
  `short_text9` varchar(50) DEFAULT NULL,
  `short_text10` varchar(50) DEFAULT NULL,
  `short_text11` varchar(50) DEFAULT NULL,
  `short_text12` varchar(50) DEFAULT NULL,
  `short_text13` varchar(50) DEFAULT NULL,
  `short_text14` varchar(50) DEFAULT NULL,
  `short_text15` varchar(50) DEFAULT NULL,
  `short_text16` varchar(50) DEFAULT NULL,
  `short_text17` varchar(50) DEFAULT NULL,
  `short_text18` varchar(50) DEFAULT NULL,
  `short_text19` varchar(50) DEFAULT NULL,
  `short_text20` varchar(50) DEFAULT NULL,
  `short_text21` varchar(50) DEFAULT NULL,
  `short_text22` varchar(50) DEFAULT NULL,
  `short_text23` varchar(50) DEFAULT NULL,
  `short_text24` varchar(50) DEFAULT NULL,
  `short_text25` varchar(50) DEFAULT NULL,
  `medium_text1` varchar(200) DEFAULT NULL,
  `medium_text2` varchar(200) DEFAULT NULL,
  `medium_text3` varchar(200) DEFAULT NULL,
  `medium_text4` varchar(200) DEFAULT NULL,
  `medium_text5` varchar(200) DEFAULT NULL,
  `medium_text6` varchar(200) DEFAULT NULL,
  `medium_text7` varchar(200) DEFAULT NULL,
  `medium_text8` varchar(200) DEFAULT NULL,
  `medium_text9` varchar(200) DEFAULT NULL,
  `medium_text10` varchar(200) DEFAULT NULL,
  `medium_text11` varchar(200) DEFAULT NULL,
  `medium_text12` varchar(200) DEFAULT NULL,
  `medium_text13` varchar(200) DEFAULT NULL,
  `medium_text14` varchar(200) DEFAULT NULL,
  `medium_text15` varchar(200) DEFAULT NULL,
  `medium_text16` varchar(200) DEFAULT NULL,
  `medium_text17` varchar(200) DEFAULT NULL,
  `medium_text18` varchar(200) DEFAULT NULL,
  `medium_text19` varchar(200) DEFAULT NULL,
  `medium_text20` varchar(200) DEFAULT NULL,
  `medium_text21` varchar(200) DEFAULT NULL,
  `medium_text22` varchar(200) DEFAULT NULL,
  `medium_text23` varchar(200) DEFAULT NULL,
  `medium_text24` varchar(200) DEFAULT NULL,
  `medium_text25` varchar(200) DEFAULT NULL,
  `large_text1` varchar(2000) DEFAULT NULL,
  `large_text2` varchar(2000) DEFAULT NULL,
  `large_text3` varchar(2000) DEFAULT NULL,
  `large_text4` varchar(2000) DEFAULT NULL,
  `clob_text1` mediumtext,
  `long1` bigint DEFAULT NULL,
  `long2` bigint DEFAULT NULL,
  `long3` bigint DEFAULT NULL,
  `long4` bigint DEFAULT NULL,
  `long5` bigint DEFAULT NULL,
  `long6` bigint DEFAULT NULL,
  `long7` bigint DEFAULT NULL,
  `long8` bigint DEFAULT NULL,
  `long9` bigint DEFAULT NULL,
  `long10` bigint DEFAULT NULL,
  `double1` double(255,0) DEFAULT NULL,
  `double2` double(255,0) DEFAULT NULL,
  `double3` double(255,0) DEFAULT NULL,
  `double4` double(255,0) DEFAULT NULL,
  `double5` double(255,0) DEFAULT NULL,
  `double6` double(255,0) DEFAULT NULL,
  `double7` double(255,0) DEFAULT NULL,
  `double8` double(255,0) DEFAULT NULL,
  `double9` double(255,0) DEFAULT NULL,
  `double10` double(255,0) DEFAULT NULL,
  `date1` datetime DEFAULT NULL,
  `date2` datetime DEFAULT NULL,
  `date3` datetime DEFAULT NULL,
  `date4` datetime DEFAULT NULL,
  `date5` datetime DEFAULT NULL,
  `date6` datetime DEFAULT NULL,
  `date7` datetime DEFAULT NULL,
  `date8` datetime DEFAULT NULL,
  `date9` datetime DEFAULT NULL,
  `date10` datetime DEFAULT NULL,
  PRIMARY KEY (`data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE `cms_exd_default` (
  `data_id` bigint NOT NULL COMMENT '关联数据ID',
  `data_type` varchar(20) NOT NULL COMMENT '关联数据类型',
  `model_id` bigint NOT NULL COMMENT '元数据模型ID',
  `short_text1` varchar(50) DEFAULT NULL,
  `short_text2` varchar(50) DEFAULT NULL,
  `short_text3` varchar(50) DEFAULT NULL,
  `short_text4` varchar(50) DEFAULT NULL,
  `short_text5` varchar(50) DEFAULT NULL,
  `short_text6` varchar(50) DEFAULT NULL,
  `short_text7` varchar(50) DEFAULT NULL,
  `short_text8` varchar(50) DEFAULT NULL,
  `short_text9` varchar(50) DEFAULT NULL,
  `short_text10` varchar(50) DEFAULT NULL,
  `short_text11` varchar(50) DEFAULT NULL,
  `short_text12` varchar(50) DEFAULT NULL,
  `short_text13` varchar(50) DEFAULT NULL,
  `short_text14` varchar(50) DEFAULT NULL,
  `short_text15` varchar(50) DEFAULT NULL,
  `short_text16` varchar(50) DEFAULT NULL,
  `short_text17` varchar(50) DEFAULT NULL,
  `short_text18` varchar(50) DEFAULT NULL,
  `short_text19` varchar(50) DEFAULT NULL,
  `short_text20` varchar(50) DEFAULT NULL,
  `short_text21` varchar(50) DEFAULT NULL,
  `short_text22` varchar(50) DEFAULT NULL,
  `short_text23` varchar(50) DEFAULT NULL,
  `short_text24` varchar(50) DEFAULT NULL,
  `short_text25` varchar(50) DEFAULT NULL,
  `medium_text1` varchar(200) DEFAULT NULL,
  `medium_text2` varchar(200) DEFAULT NULL,
  `medium_text3` varchar(200) DEFAULT NULL,
  `medium_text4` varchar(200) DEFAULT NULL,
  `medium_text5` varchar(200) DEFAULT NULL,
  `medium_text6` varchar(200) DEFAULT NULL,
  `medium_text7` varchar(200) DEFAULT NULL,
  `medium_text8` varchar(200) DEFAULT NULL,
  `medium_text9` varchar(200) DEFAULT NULL,
  `medium_text10` varchar(200) DEFAULT NULL,
  `medium_text11` varchar(200) DEFAULT NULL,
  `medium_text12` varchar(200) DEFAULT NULL,
  `medium_text13` varchar(200) DEFAULT NULL,
  `medium_text14` varchar(200) DEFAULT NULL,
  `medium_text15` varchar(200) DEFAULT NULL,
  `medium_text16` varchar(200) DEFAULT NULL,
  `medium_text17` varchar(200) DEFAULT NULL,
  `medium_text18` varchar(200) DEFAULT NULL,
  `medium_text19` varchar(200) DEFAULT NULL,
  `medium_text20` varchar(200) DEFAULT NULL,
  `medium_text21` varchar(200) DEFAULT NULL,
  `medium_text22` varchar(200) DEFAULT NULL,
  `medium_text23` varchar(200) DEFAULT NULL,
  `medium_text24` varchar(200) DEFAULT NULL,
  `medium_text25` varchar(200) DEFAULT NULL,
  `large_text1` varchar(2000) DEFAULT NULL,
  `large_text2` varchar(2000) DEFAULT NULL,
  `large_text3` varchar(2000) DEFAULT NULL,
  `large_text4` varchar(2000) DEFAULT NULL,
  `clob_text1` mediumtext,
  `long1` bigint DEFAULT NULL,
  `long2` bigint DEFAULT NULL,
  `long3` bigint DEFAULT NULL,
  `long4` bigint DEFAULT NULL,
  `long5` bigint DEFAULT NULL,
  `long6` bigint DEFAULT NULL,
  `long7` bigint DEFAULT NULL,
  `long8` bigint DEFAULT NULL,
  `long9` bigint DEFAULT NULL,
  `long10` bigint DEFAULT NULL,
  `double1` double(255,0) DEFAULT NULL,
  `double2` double(255,0) DEFAULT NULL,
  `double3` double(255,0) DEFAULT NULL,
  `double4` double(255,0) DEFAULT NULL,
  `double5` double(255,0) DEFAULT NULL,
  `double6` double(255,0) DEFAULT NULL,
  `double7` double(255,0) DEFAULT NULL,
  `double8` double(255,0) DEFAULT NULL,
  `double9` double(255,0) DEFAULT NULL,
  `double10` double(255,0) DEFAULT NULL,
  `date1` datetime DEFAULT NULL,
  `date2` datetime DEFAULT NULL,
  `date3` datetime DEFAULT NULL,
  `date4` datetime DEFAULT NULL,
  `date5` datetime DEFAULT NULL,
  `date6` datetime DEFAULT NULL,
  `date7` datetime DEFAULT NULL,
  `date8` datetime DEFAULT NULL,
  `date9` datetime DEFAULT NULL,
  `date10` datetime DEFAULT NULL,
  PRIMARY KEY (`data_id`,`data_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

INSERT INTO `sys_menu` VALUES (430649774219333, '自定义表单', 2035, 5, 'customform', 'cms/customform/index', NULL, 'N', 'Y', 'C', 'Y', '0', 'cms:custom:view', 'form', 'admin', '2023-06-20 10:12:08', 'admin', '2023-06-20 10:13:09', '');

INSERT INTO `sys_i18n_dict` VALUES (null, 'zh-CN', 'MENU.NAME.430649774219333', '自定义表单');
INSERT INTO `sys_i18n_dict` VALUES (null, 'en', 'MENU.NAME.430649774219333', 'Custom Form');

ALTER TABLE cms_content add column deleted tinyint DEFAULT 0;
ALTER TABLE cms_article_detail add column deleted tinyint DEFAULT 0;
ALTER TABLE cms_image add column deleted tinyint DEFAULT 0;
ALTER TABLE cms_audio add column deleted tinyint DEFAULT 0;
ALTER TABLE cms_video add column deleted tinyint DEFAULT 0;

ALTER TABLE sys_config modify column config_id bigint;
ALTER TABLE sys_notice modify column notice_id bigint;

DROP TABLE x_model_data;
DROP TABLE cms_content_backup;
DROP TABLE cms_article_detail_backup;
DROP TABLE cms_image_backup;
DROP TABLE cms_audio_backup;
DROP TABLE cms_video_backup;

alter table cc_comment drop column del_flag;