<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>chestnut</artifactId>
		<groupId>com.chestnut</groupId>
		<version>1.4.5</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>chestnut-admin</artifactId>

	<description>
        web服务入口
    </description>

	<dependencies>

		<!-- spring-boot-devtools -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional> <!-- 表示依赖不会传递 -->
		</dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>  
        </dependency>
        
		<!-- 系统模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-system</artifactId>
		</dependency>

		<!-- 代码生成 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-generator</artifactId>
		</dependency>

		<!-- 元数据模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-meta</artifactId>
		</dependency>

		<!-- 数据统计模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-stat</artifactId>
		</dependency>

		<!-- 会员模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-member</artifactId>
		</dependency>

		<!-- 文章 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-article</artifactId>
		</dependency>

		<!-- 广告 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-advertisement</artifactId>
		</dependency>

		<!-- 页面区块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-block</artifactId>
		</dependency>

		<!-- 扩展模型 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-exmodel</artifactId>
		</dependency>

		<!-- 图集 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-image</artifactId>
		</dependency>

		<!-- 音视频集 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-media</artifactId>
		</dependency>

		<!-- 友链 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-link</artifactId>
		</dependency>

		<!-- 词汇 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-word</artifactId>
		</dependency>

		<!-- 内容索引 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-search</artifactId>
		</dependency>
		
		<!-- CMS评论模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-comment</artifactId>
		</dependency>
		
		<!-- CMS调查投票模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-vote</artifactId>
		</dependency>
		
		<!-- CMS访问统计模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-stat</artifactId>
		</dependency>

		<!-- CMS自定义表单模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-customform</artifactId>
		</dependency>

		<!-- CMS会员扩展模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-member</artifactId>
		</dependency>

		<!-- SEO模块 -->
		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-seo</artifactId>
		</dependency>

		<dependency>
			<groupId>com.chestnut</groupId>
			<artifactId>chestnut-cms-dynamic</artifactId>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<!--spring-boot打包插件-->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
				<configuration>
					<!-- 如果没有该配置，devtools不会生效 -->
					<!-- <fork>true</fork> -->
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<finalName>${project.artifactId}</finalName>
	</build>

</project>