[mysqld]
datadir=/var/lib/mysql/data
port = 3306
socket=/tmp/mysql.sock

symbolic-links=0
#log-error=/var/log/mysqld.log
pid-file=/tmp/mysqld/mysqld.pid
sql_mode='STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'

[client]
default-character-set=utf8

[mysql]
default-character-set=utf8

[mysqld]
log-bin=mysql-bin
binlog-format=ROW
server_id=1
max_connections=1000
lower_case_table_names=1
#user=mysql

init_connect='SET collation_connection = utf8_general_ci'
init_connect='SET NAMES utf8'
character-set-server=utf8
collation-server=utf8_general_ci
skip-character-set-client-handshake
