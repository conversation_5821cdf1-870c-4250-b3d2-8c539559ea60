version: '3'
networks:
  default:
    external:
      name: cc_bridge
services: 
  cc-mysql:
    image: mysql:8.0.32
    container_name: cc-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=hello1234
      - TZ=Asia/Shanghai
    networks:
      - default
    ports:
      - '3306:3306'
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf/my.cnf:/etc/my.cnf
      - ./mysql/init:/docker-entrypoint-initdb.d
      
  cc-redis:
    image: redis:6.2.13
    container_name: cc-redis
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 6379:6379
    volumes:
      - "./redis/conf:/usr/local/etc/redis"
      - "./redis/data:/data"
    command:
      redis-server --port 6379 --requirepass "b18a03" --appendonly yes
    networks:
      - default
      
  cc-minio:
    image: minio/minio:latest
    container_name: cc-minio
    ports:
      - 9000:9000
      - 9999:9999
    volumes:
      - ./minio/data:/data
      - ./minio/config:/root/.minio
    environment:
      MINIO_ROOT_USER: "root"
      MINIO_ROOT_PASSWORD: "minioadmin"
      MINIO_ACCESS_KEY: minioccadmin
      MINIO_SECRET_KEY: minioccadmin
    logging:
      options:
        max-size: "50M" # 最大文件上传限制
        max-file: "10"
      driver: json-file
    command: server /data --console-address ":9999"
    restart: always
    networks:
      - default
      
  cc-xxl-job-admin:
    image: xuxueli/xxl-job-admin:2.4.0
    restart: unless-stopped
    container_name: cc-xxl-job-admin
    ports:                                                
      - 18080:8080
    environment:
      TZ: Asia/Shanghai
      PARAMS: "--spring.datasource.url=********************************************************************************************************************* --spring.datasource.username=root --spring.datasource.password=xxxxxx"
    volumes:
      - ./xxl-job/logs:/data/applogs
    depends_on:
      - cc-mysql
    networks:
      - default
      
  cc-elasticsearch:
    # 构建镜像的相关配置在 chestnut-modules/chestnut-search/docker 目录下
    image: elasticsearch-ik:8.5.2
    restart: unless-stopped
    container_name: cc-elasticsearch
    healthcheck:
      test: [ "CMD-SHELL", "curl --silent --fail localhost:9200/_cluster/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    ports:                                                
      - 9200:9200                                          
      - 9300:9300                                         
    environment:
      discovery.type: single-node
      ES_JAVA_OPTS: -Xms1024m -Xmx1024m
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs
      - ./elasticsearch/ik-config:/usr/share/elasticsearch/plugins/ik/config
    ulimits:
      memlock:
        soft: -1
        hard: -1
    networks:
      - default