version: '3'
services:
  chestnut-cms:
    image: registry.cn-shanghai.aliyuncs.com/aaron2im/chestnut-admin:1.0.0
    container_name: chestnut-cms
    restart: always
    environment:
      SERVER_PORT: 48082
      SPRING_PROFILES_ACTIVE: dev
      JVM_OPTS: "-Xms2048M -Xmx2048m -Xmn512m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
    ports:
      - "48082:48082"
      - "8899:8899"
    volumes:
      - /opt/chestnut/chestnut-admin.jar:/home/<USER>/app.jar
      - /opt/chestnut/application-dev.yml:/home/<USER>/application-dev.yml
      - ./logs:/home/<USER>/logs
      - ./app/uploadPath:/home/<USER>/uploadPath
      - ./app/_xy_member:/home/<USER>/_xy_member
      - ./app/statics:/home/<USER>/statics
      - ./app/wwwroot_release:/home/<USER>/wwwroot_release
networks:
  proxy:
    ipam:
      config:
        - subnet: ********/24