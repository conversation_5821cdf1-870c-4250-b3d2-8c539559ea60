/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.seo.job;

import com.chestnut.seo.service.BaiduSitemapService;
import com.chestnut.system.schedule.IScheduledHandler;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 站点地图定时更新任务
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@RequiredArgsConstructor
@Component(IScheduledHandler.BEAN_PREFIX + SitemapJobHandler.JOB_NAME)
public class SitemapJobHandler extends IJobHandler implements IScheduledHandler {

	static final String JOB_NAME = "SiteMapJobHandler";

	private final BaiduSitemapService bdSitemapService;

	@Override
	public String getId() {
		return JOB_NAME;
	}

	@Override
	public String getName() {
		return "{SCHEDULED_TASK." + JOB_NAME + "}";
	}

	@Override
	public void exec() throws Exception {
		IScheduledHandler.logger.info("Job start: {}", JOB_NAME);
		long s = System.currentTimeMillis();
		this.bdSitemapService.generateSitemapXml();
		IScheduledHandler.logger.info("Job '{}' completed, cost: {}ms", JOB_NAME, System.currentTimeMillis() - s);
	}

	@Override
	@XxlJob(JOB_NAME)
	public void execute() throws Exception {
		this.exec();
	}
}
