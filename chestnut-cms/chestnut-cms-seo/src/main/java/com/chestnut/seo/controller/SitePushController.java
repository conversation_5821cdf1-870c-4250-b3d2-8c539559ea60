/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.seo.controller;

import com.chestnut.common.domain.R;
import com.chestnut.common.security.anno.Priv;
import com.chestnut.common.security.web.BaseRestController;
import com.chestnut.common.utils.ServletUtils;
import com.chestnut.contentcore.domain.CmsSite;
import com.chestnut.contentcore.service.ISiteService;
import com.chestnut.seo.service.BaiduPushService;
import com.chestnut.system.security.AdminUserType;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 百度收录推送前端控制器
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/cms/seo")
@RequiredArgsConstructor
public class SitePushController extends BaseRestController {

	private final ISiteService siteService;

	private final BaiduPushService baiduPushService;

	@Priv(type = AdminUserType.TYPE)
	@PostMapping("/baidu_push")
	public R<?> generateSitemap(@RequestBody @NotEmpty List<Long> contentIds) {
		CmsSite site = siteService.getCurrentSite(ServletUtils.getRequest());

		List<BaiduPushService.BaiduPushResult> results = baiduPushService.pushContents(site, contentIds);

		return R.ok(results);
	}
}
