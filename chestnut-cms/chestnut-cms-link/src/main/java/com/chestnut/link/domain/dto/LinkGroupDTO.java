/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.link.domain.dto;

import org.springframework.beans.BeanUtils;

import com.chestnut.link.domain.CmsLinkGroup;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LinkGroupDTO {
	
    private Long linkGroupId;

    private Long siteId;

    @NotNull
    private String name;

    @NotNull
    private String code;
    
    private Long sortFlag;
    
	public static LinkGroupDTO newInstance(CmsLinkGroup linkGroup) {
		LinkGroupDTO dto = new LinkGroupDTO();
    	BeanUtils.copyProperties(linkGroup, dto);
		return dto;
	}
}
