/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.cms.dynamic.domain.vo;

import com.chestnut.cms.dynamic.core.IDynamicPageInitData;
import com.chestnut.common.i18n.I18nUtils;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义动态页面初始化数据类型VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
@Getter
@Setter
public class DynamicPageInitDataTypeVO {

    /**
     * 类型，唯一标识
     */
    private String type;

    /**
     * 名称
     */
    private String name;

    public static DynamicPageInitDataTypeVO newInstance(IDynamicPageInitData dynamicPageInitData) {
        DynamicPageInitDataTypeVO vo = new DynamicPageInitDataTypeVO();
        vo.setType(dynamicPageInitData.getType());
        vo.setName(I18nUtils.get(dynamicPageInitData.getName()));
        return vo;
    }
}
