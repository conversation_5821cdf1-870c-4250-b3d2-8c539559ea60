# 资源类型
CMS.CONTENTCORE.RESOURCE_TYPE.image=图片
CMS.CONTENTCORE.RESOURCE_TYPE.audio=音频
CMS.CONTENTCORE.RESOURCE_TYPE.video=视频
CMS.CONTENTCORE.RESOURCE_TYPE.file=文件

# 栏目类型
CMS.CONTENTCORE.CATALOG_TYPE.common=普通栏目
CMS.CONTENTCORE.CATALOG_TYPE.link=链接栏目

# 字典数据
DICT.CMSPageWidgetStatus=内容状态
DICT.CMSPageWidgetStatus.0=初稿
DICT.CMSPageWidgetStatus.30=已发布
DICT.CMSPageWidgetStatus.40=已下线
DICT.CMSPageWidgetStatus.60=重新编辑
DICT.CMSContentStatus=内容状态
DICT.CMSContentStatus.0=初稿
DICT.CMSContentStatus.20=待发布
DICT.CMSContentStatus.30=已发布
DICT.CMSContentStatus.40=已下线
DICT.CMSContentStatus.60=重新编辑
DICT.CMSContentAttribute=内容属性
DICT.CMSContentAttribute.image=图片
DICT.CMSContentAttribute.video=视频
DICT.CMSContentAttribute.attach=附件
DICT.CMSContentAttribute.hot=热点
DICT.CMSContentAttribute.recommend=推荐
DICT.CMSStaticSuffix=静态文件类型
DICT.CMSStaticSuffix.shtml=shtml
DICT.CMSStaticSuffix.html=html
DICT.CMSStaticSuffix.xml=xml
DICT.CMSStaticSuffix.json=json
DICT.CMSContentFlowTaskState.0=无需审核
DICT.CMSContentFlowTaskState.10=未发起
DICT.CMSContentFlowTaskState.20=待审批
DICT.CMSContentFlowTaskState.30=已通过
DICT.CMSContentFlowTaskState.40=已退回
DICT.CMSContentFlowTaskState.50=已撤回

# 固定配置参数
CONFIG.CMSBackendContext=后台访问地址
CONFIG.CMSModuleEnable=是否开启CMS功能
CONFIG.CMSTemplateSuffix=模板后缀名
CONFIG.AllowUploadFileType=CMS文件上传类型限制
CONFIG.SiteApiUrl=站点API地址

# 错误信息
ERRCODE.CMS.CONTENTCORE.NO_SITE=无站点数据
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_PAGE_WIDGET_TYPE=未知页面部件类型：{0}
ERRCODE.CMS.CONTENTCORE.TEMPLATE_EMPTY=模板未配置或模板文件不存在
ERRCODE.CMS.CONTENTCORE.TEMPLATE_FILE_NOT_FOUND=模板文件“{0}”不存在
ERRCODE.CMS.CONTENTCORE.INVALID_TEMPLATE_NAME=模板文件名只能使用大小写字母和下划线，且必须以`{0}`结尾
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CONTENT_TYPE=未知内容类型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CATALOG_TYPE=未知栏目类型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_INTERNAL_DATA_TYPE=未知内部数据类型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_RESOURCE_TYPE=未知资源类型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_DYNAMIC_PAGE_TYPE=未知动态模板类型：{0}
ERRCODE.CMS.CONTENTCORE.DEL_CHILD_FIRST=请先删除子栏目
ERRCODE.CMS.CONTENTCORE.CONFLICT_CATALOG=栏目名称/别名/目录重复
ERRCODE.CMS.CONTENTCORE.CATALOG_MAX_TREE_LEVEL=栏目层级超出上限
ERRCODE.CMS.CONTENTCORE.CATALOG_MOVE_TO_SELF_OR_CHILD=栏目不能转移到自身或子栏目
ERRCODE.CMS.CONTENTCORE.INVALID_PROPERTY=扩展属性[{0}={1}]校验失败
ERRCODE.CMS.CONTENTCORE.EXISTS_SITE_PATH=站点目录冲突
ERRCODE.CMS.CONTENTCORE.CONTENT_LOCKED=内容已被"{0}"锁定
ERRCODE.CMS.CONTENTCORE.TITLE_REPLEAT=标题重复
ERRCODE.CMS.CONTENTCORE.CANNOT_EDIT_PUBLISHED_CONTENT=已发布内容不允许编辑，请先下线内容！
ERRCODE.CMS.CONTENTCORE.NO_PUBLISHPIPE=无可用发布通道
ERRCODE.CMS.CONTENTCORE.CATALOG_CANNOT_PUBLISH=栏目发布失败：栏目不可见/不可静态化/标题栏目。
ERRCODE.CMS.CONTENTCORE.SITE_FILE_OP_ERR=不能操作非当前站点文件
ERRCODE.CMS.CONTENTCORE.TEMPLATE_PATH_EXISTS=模板文件已存在或路径被占用
ERRCODE.CMS.CONTENTCORE.NOT_ALLOW_FILE_TYPE=禁止上传的文件类型：{0}
ERRCODE.CMS.CONTENTCORE.NOT_EDITABLE_FILE=指定文件不支持在线编辑
ERRCODE.CMS.CONTENTCORE.FILE_NOT_EXIST=文件不存在
ERRCODE.CMS.CONTENTCORE.FILE_ALREADY_EXISTS=文件已存在
ERRCODE.CMS.CONTENTCORE.CATALOG_SORT_VALUE_ZERO=栏目排序值不能为0
ERRCODE.CMS.CONTENTCORE.SITE_EXPORT_TASK_EXISTS=站点导出任务正在进行中
ERRCODE.CMS.CONTENTCORE.DEL_CONTENT_ERR=只能删除初稿和已下线内容
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_NO_PASSED=存在需要审批且未通过的内容，不可操作
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_IN_FLOW=存在进入审批流程的内容，不可操作

# freemarker模板标签
FREEMARKER.TAG.NAME.cms_site=站点列表标签
FREEMARKER.TAG.DESC.cms_site=获取站点数据列表，内嵌<#list DataList as site>${catalog.site}</#list>遍历数据
FREEMARKER.TAG.NAME.cms_catalog=栏目列表标签
FREEMARKER.TAG.DESC.cms_catalog=获取栏目数据列表，内嵌<#list DataList as catalog>${catalog.name}</#list>遍历数据
FREEMARKER.TAG.NAME.cms_content=内容列表标签
FREEMARKER.TAG.DESC.cms_content=获取内容数据列表，内嵌<#list DataList as content>${content.name}</#list>遍历数据
FREEMARKER.TAG.NAME.cms_site_property=站点自定义属性标签
FREEMARKER.TAG.DESC.cms_site_property=获取站点自定义属性数据列表，内嵌<#list DataList as prop>${prop.propName}</#list>遍历数据
FREEMARKER.TAG.NAME.cms_include=模板引用标签
FREEMARKER.TAG.DESC.cms_include=引用其他模板内容，支持ssi引用标签
FREEMARKER.TAG.NAME.cms_pagewidget=页面部件引用标签
FREEMARKER.TAG.DESC.cms_pagewidget=引用页面部件内容，支持ssi引用标签
FREEMARKER.TAG.NAME.cms_pagewidget_data=页面部件数据标签
FREEMARKER.TAG.DESC.cms_pagewidget_data=页面部件数据标签，标签体内可使用${Data.name}获取数据
FREEMARKER.TAG.NAME.cms_content_closest=内容前后篇标签
FREEMARKER.TAG.DESC.cms_content_closest=内容前后篇标签，仅支持指定内容当前栏目列表，标签体内可使用${Data.title}获取数据
FREEMARKER.TAG.NAME.cms_content_rela=相关内容标签
FREEMARKER.TAG.DESC.cms_content_rela=相关内容标签，内嵌<#list DataList as content>${content.name}</#list>遍历数据

# freemarker模板函数
FREEMARKER.FUNC.DESC.clearHtmlTag=清除Html标签，例如：${clearHtmlTag(ArticleContent)}
FREEMARKER.FUNC.DESC.dateFormat=日期格式化，例如：${dateFormat(content.publishDate)}
FREEMARKER.FUNC.DESC.htmlInternalUrl=将html文本中的内部链接地址“iurl://”解析为正常http(s)访问地址，例如：${htmlInternalUrl(ArticleContent)}
FREEMARKER.FUNC.DESC.imageSize=获得图片缩放图函数，不在的缩略图会自动创建，例如：${imageSize(content.logo, 300, 300)}
FREEMARKER.FUNC.DESC.internalUrl=将内部链接“iurl://”解析为正常http(s)访问地址，例如：${internalUrl(content.redirectUrl)}
FREEMARKER.FUNC.DESC.randomInt=获取指定范围随机数，例如：${randomInt(10,100)}
FREEMARKER.FUNC.DESC.siteUrl=获取站点指定发布通道访问链接，例如：${siteUrl(Site.siteId, 'h5')}
FREEMARKER.FUNC.DESC.catalogUrl=获取栏目指定发布通道访问链接，例如：${catalogUrl(Catalog.catalogId, 'h5')}
FREEMARKER.FUNC.DESC.contentUrl=获取内容指定发布通道访问链接，例如：${contnetUrl(Content.contentId, 'h5')}
FREEMARKER.FUNC.DESC.ifElse=三元表达式函数，例如：${ifElse(content.logo??, 'x1', 'x2')}
FREEMARKER.FUNC.DESC.dynamicPageLink=动态页面链接获取函数，例如：${dynamicPageLink('Search')}
FREEMARKER.FUNC.DESC.dict=获取字典数据列表，例如：${dict('YesOrNo', 'Y')}
FREEMARKER.FUNC.DESC.sysConfig=获取系统参数配置值，例如：${sysConfig('SiteApiUrl')}
FREEMARKER.FUNC.DESC.listHtmlInternalUrl=获取html文本中的iurl列表，例如：${listHtmlInternalUrl(ArticleContent)}
FREEMARKER.FUNC.DESC.contentPageLink=获取内容分页链接，例如：${contentPageLink(content.link, 2)}
FREEMARKER.FUNC.DESC.videoPlayer=将html文本中的视频资源链接替换为<video>视频播放器
FREEMARKER.FUNC.videoPlayer.Arg1.Name=Html文本内容
FREEMARKER.FUNC.videoPlayer.Arg2.Name=视频宽度
FREEMARKER.FUNC.videoPlayer.Arg2.Desc=默认：100%
FREEMARKER.FUNC.videoPlayer.Arg3.Name=视频高度

# 校验规则
VALIDATOR.CMS.SITE.PUBLISH_PIPE_PROPS_EMPTY=发布通道配置不能为空

# 定时任务
SCHEDULED_TASK.RecycleExpireJobHandler=回收站过期内容删除任务
SCHEDULED_TASK.SitePublishJobHandler=定时发布任务
SCHEDULED_TASK.ContentTopCancelJobHandler=内容置顶取消任务
SCHEDULED_TASK.UpdateDynamicDataJobHandler=保存内容动态数据任务
SCHEDULED_TASK.ContentOfflineJobHandler=内容定时下线任务