# 资源类型
CMS.CONTENTCORE.RESOURCE_TYPE.image=Image
CMS.CONTENTCORE.RESOURCE_TYPE.audio=Audio
CMS.CONTENTCORE.RESOURCE_TYPE.video=Video
CMS.CONTENTCORE.RESOURCE_TYPE.file=File

# 栏目类型
CMS.CONTENTCORE.CATALOG_TYPE.common=Common
CMS.CONTENTCORE.CATALOG_TYPE.link=Link

# 字典数据
DICT.CMSPageWidgetStatus=Page Widget Status
DICT.CMSPageWidgetStatus.0=Draft
DICT.CMSPageWidgetStatus.30=Published
DICT.CMSPageWidgetStatus.40=Offline
DICT.CMSPageWidgetStatus.60=Editing
DICT.CMSContentStatus=Content Status
DICT.CMSContentStatus.0=Draft
DICT.CMSContentStatus.20=ToPublish
DICT.CMSContentStatus.30=Published
DICT.CMSContentStatus.40=Offline
DICT.CMSContentStatus.60=Editing
DICT.CMSContentAttribute=Content Attribute
DICT.CMSContentAttribute.image=Image
DICT.CMSContentAttribute.video=Video
DICT.CMSContentAttribute.attach=Attachment
DICT.CMSContentAttribute.hot=Hot
DICT.CMSContentAttribute.recommend=Recommend
DICT.CMSStaticSuffix=Static File Type
DICT.CMSStaticSuffix.shtml=shtml
DICT.CMSStaticSuffix.html=html
DICT.CMSStaticSuffix.xml=xml
DICT.CMSStaticSuffix.json=json
DICT.CMSContentFlowTaskState.0=No Flow
DICT.CMSContentFlowTaskState.10=No Start
DICT.CMSContentFlowTaskState.20=Pending
DICT.CMSContentFlowTaskState.30=Passed
DICT.CMSContentFlowTaskState.40=Backed
DICT.CMSContentFlowTaskState.50=Terminated

# 固定配置参数
CONFIG.CMSBackendContext=Backend Context
CONFIG.CMSModuleEnable=CMS Module Switch
CONFIG.CMSTemplateSuffix=Template Suffix
CONFIG.AllowUploadFileType=CMS Allow Upload File Types
CONFIG.SiteApiUrl=Site Api Url

# 错误信息
ERRCODE.CMS.CONTENTCORE.NO_SITE=No site.
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_PAGE_WIDGET_TYPE=Unsupported page widget "{0}".
ERRCODE.CMS.CONTENTCORE.TEMPLATE_EMPTY=The template is not configured or doesn't exists.
ERRCODE.CMS.CONTENTCORE.TEMPLATE_FILE_NOT_FOUND=The template file "{0}" not found.
ERRCODE.CMS.CONTENTCORE.INVALID_TEMPLATE_NAME=The template nam  only [A-Za-z0-9_]+ can be used and must ends with `{0}`.
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CONTENT_TYPE=Unsupported content type "{0}".
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CATALOG_TYPE=Unsupported catalog type "{0}".
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_INTERNAL_DATA_TYPE=Unsupported internal data type "{0}".
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_RESOURCE_TYPE=Unsupported resource type "{0}".
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_DYNAMIC_PAGE_TYPE=Unsupported dynamic page type "{0}".
ERRCODE.CMS.CONTENTCORE.DEL_CHILD_FIRST=Delete the child catalog first.
ERRCODE.CMS.CONTENTCORE.CONFLICT_CATALOG=Catalog name/alias/path conflict.
ERRCODE.CMS.CONTENTCORE.CATALOG_MAX_TREE_LEVEL=The catalog tree level exceeds max.
ERRCODE.CMS.CONTENTCORE.CATALOG_MOVE_TO_SELF_OR_CHILD=The catalog cannot move to self or child.
ERRCODE.CMS.CONTENTCORE.INVALID_PROPERTY=Invalid property: {0} = {1}
ERRCODE.CMS.CONTENTCORE.EXISTS_SITE_PATH=Site directory conflict.
ERRCODE.CMS.CONTENTCORE.CONTENT_LOCKED=The content is locked by "{0}".
ERRCODE.CMS.CONTENTCORE.TITLE_REPLEAT=The title is conflict.
ERRCODE.CMS.CONTENTCORE.CANNOT_EDIT_PUBLISHED_CONTENT=Cannot edit published content.
ERRCODE.CMS.CONTENTCORE.NO_PUBLISHPIPE=No publish pipe found.
ERRCODE.CMS.CONTENTCORE.CATALOG_CANNOT_PUBLISH=Catalog publish failed: invisible or unstaticize or link type.
ERRCODE.CMS.CONTENTCORE.SITE_FILE_OP_ERR=Cannot modify file which not belong current site.
ERRCODE.CMS.CONTENTCORE.TEMPLATE_PATH_EXISTS=The template file path is already exists.
ERRCODE.CMS.CONTENTCORE.NOT_ALLOW_FILE_TYPE=The file type "{0}" rejected for upload.
ERRCODE.CMS.CONTENTCORE.NOT_EDITABLE_FILE=The file is not editable.
ERRCODE.CMS.CONTENTCORE.FILE_NOT_EXIST=File not exist.
ERRCODE.CMS.CONTENTCORE.FILE_ALREADY_EXISTS=The file is alreay exists.
ERRCODE.CMS.CONTENTCORE.CATALOG_SORT_VALUE_ZERO=Catalog sort value cannot be 0.
ERRCODE.CMS.CONTENTCORE.SITE_EXPORT_TASK_EXISTS=Site export task is running...
ERRCODE.CMS.CONTENTCORE.DEL_CONTENT_ERR=Only draft or offline content can be deleted.
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_NO_PASSED=Exist content that needs to be approved but fails , cannot be performed.
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_IN_FLOW=Exist content that into flow , cannot be performed.

# 模板freemarker
FREEMARKER.TAG.NAME.cms_site=Site list tag
FREEMARKER.TAG.DESC.cms_site=Fetch site list, use <#list> in tag like "<#list DataList as site>${site.name}</#list>" to walk through the list of sites.
FREEMARKER.TAG.NAME.cms_catalog=Catalog list tag
FREEMARKER.TAG.DESC.cms_catalog=Fetch catalog list, use <#list> in tag like "<#list DataList as catalog>${catalog.name}</#list>" to walk through the list of catalogs.
FREEMARKER.TAG.NAME.cms_content=Content list tag
FREEMARKER.TAG.DESC.cms_content=Fetch content list, use <#list> in tag like "<#list DataList as content>${content.title}</#list>" to walk through the list of contents.
FREEMARKER.TAG.NAME.cms_site_property=Site custom property list tag
FREEMARKER.TAG.DESC.cms_site_property=Fetch site custom property list, use <#list> in tag like "<#list DataList as prop>${prop.propName}</#list>" to walk through the list of properties.
FREEMARKER.TAG.NAME.cms_include=Include tag
FREEMARKER.TAG.DESC.cms_include=Use the tag to incude other template, supported ssi mode.
FREEMARKER.TAG.NAME.cms_pagewidget=Pagewidget tag
FREEMARKER.TAG.DESC.cms_pagewidget=Include pagewidget in to template, supported ssi mode.
FREEMARKER.TAG.NAME.cms_pagewidget_data=Pagewidget data tag
FREEMARKER.TAG.DESC.cms_pagewidget_data=Fetch pagewidget data and use "${Data.name}" to show in template.
FREEMARKER.TAG.NAME.cms_content_closest=Content closest tag
FREEMARKER.TAG.DESC.cms_content_closest=Only support the content's catalog to fetch data, use <#list> in tag like "<#list DataList as content>${content.title}</#list>" to walk through the list of contents.
FREEMARKER.TAG.NAME.cms_content_rela=Related content tag
FREEMARKER.TAG.DESC.cms_content_rela=Fetch related contents by contentId, use <#list> in tag like "<#list DataList as content>${content.title}</#list>" to walk through the list of contents.

# freemarker模板函数
FREEMARKER.FUNC.DESC.clearHtmlTag=Use ${clearHtmlTag(ArticleContent)} in template to clear html tag.
FREEMARKER.FUNC.DESC.dateFormat=Use ${dateFormat(site.publishDate,'yyyy-MM')} in template to format the date.
FREEMARKER.FUNC.DESC.htmlInternalUrl=Use ${htmlInternalUrl(ArticleContent)} in template to parse all "iurl://xx" in the content to http(s) link.
FREEMARKER.FUNC.DESC.imageSize=Use ${imageSize(site.logo, 300, 300)} in template to get the thumbnal image, thumbnail will create if not exists.
FREEMARKER.FUNC.DESC.internalUrl=Use ${internalUrl(site.logo)} in template to parse "iurl://xx" to http(s) link.
FREEMARKER.FUNC.DESC.randomInt=Use ${randomInt(10,100)} in template to get a random int.
FREEMARKER.FUNC.DESC.siteUrl=Use ${siteUrl(Site.siteId,'h5')} in template to get the site publishpipe url.
FREEMARKER.FUNC.DESC.catalogUrl=Use ${catalogUrl(Catalog.catalogId,'h5')} in template to get the catalog publishpipe url.
FREEMARKER.FUNC.DESC.contentUrl=Use ${contentUrl(Content.contentId,'h5')} in template to get the content publishpipe url.
FREEMARKER.FUNC.DESC.ifElse=Use ${ifElse(Site.siteId!='','1','2')} in template to imitate the effect of ternary expressions.
FREEMARKER.FUNC.DESC.dynamicPageLink=Use ${dynamicPageLink('Search')} in template to get the dynamic page link.
FREEMARKER.FUNC.DESC.dict=Use ${dict(content.linkFlag, 'YesOrNo')} in template to get the dict data label.
FREEMARKER.FUNC.DESC.sysConfig=Use ${sysConfig('SiteApiUrl')} in template to get the system config value.
FREEMARKER.FUNC.DESC.listHtmlInternalUrl=Use ${listHtmlInternalUrl(ArticleContent)} in template to get the iurl list.
FREEMARKER.FUNC.DESC.contentPageLink=Use ${contentPageLink(content.link, 2)} in template to get the content page link.
FREEMARKER.FUNC.DESC.videoPlayer=Replace html video resource link tag to video tag.
FREEMARKER.FUNC.videoPlayer.Arg1.Name=Html content
FREEMARKER.FUNC.videoPlayer.Arg2.Name=Width
FREEMARKER.FUNC.videoPlayer.Arg2.Desc=Default：100%
FREEMARKER.FUNC.videoPlayer.Arg3.Name=Height

# 校验规则
VALIDATOR.CMS.SITE.PUBLISH_PIPE_PROPS_EMPTY=The site publish pipe props cannot be empty.

# 定时任务
SCHEDULED_TASK.RecycleExpireJobHandler=Recycle Content Expired Task
SCHEDULED_TASK.SitePublishJobHandler=Site Publish Task
SCHEDULED_TASK.ContentTopCancelJobHandler=Content Top Cancel Task
SCHEDULED_TASK.UpdateDynamicDataJobHandler=Save Content Dynamic Data Task
SCHEDULED_TASK.ContentOfflineJobHandler=Content Offline Task