# 資源類型
CMS.CONTENTCORE.RESOURCE_TYPE.image=圖片
CMS.CONTENTCORE.RESOURCE_TYPE.audio=音頻
CMS.CONTENTCORE.RESOURCE_TYPE.video=視頻
CMS.CONTENTCORE.RESOURCE_TYPE.file=檔案

# 欄目類型
CMS.CONTENTCORE.CATALOG_TYPE.common=普通欄目
CMS.CONTENTCORE.CATALOG_TYPE.link=連結欄目

# 字典數據
DICT.CMSPageWidgetStatus=內容狀態
DICT.CMSPageWidgetStatus.0=初稿
DICT.CMSPageWidgetStatus.30=已發布
DICT.CMSPageWidgetStatus.40=已下線
DICT.CMSPageWidgetStatus.60=重新編輯
DICT.CMSContentStatus=內容狀態
DICT.CMSContentStatus.0=初稿
DICT.CMSContentStatus.20=待發布
DICT.CMSContentStatus.30=已發布
DICT.CMSContentStatus.40=已下線
DICT.CMSContentStatus.60=重新編輯
DICT.CMSContentAttribute=內容屬性
DICT.CMSContentAttribute.image=圖片
DICT.CMSContentAttribute.video=視頻
DICT.CMSContentAttribute.attach=附件
DICT.CMSContentAttribute.hot=熱點
DICT.CMSContentAttribute.recommend=推薦
DICT.CMSStaticSuffix=靜態檔案類型
DICT.CMSStaticSuffix.shtml=shtml
DICT.CMSStaticSuffix.html=html
DICT.CMSStaticSuffix.xml=xml
DICT.CMSStaticSuffix.json=json
DICT.CMSContentFlowTaskState.0=無需審核
DICT.CMSContentFlowTaskState.10=未發起
DICT.CMSContentFlowTaskState.20=待審批
DICT.CMSContentFlowTaskState.30=已通過
DICT.CMSContentFlowTaskState.40=已退回
DICT.CMSContentFlowTaskState.50=已撤回

# 固定配置參數
CONFIG.CMSBackendContext=後台訪問地址
CONFIG.CMSModuleEnable=是否開啟CMS功能
CONFIG.CMSTemplateSuffix=模板尾碼名
CONFIG.AllowUploadFileType=CMS檔案上傳類型限制
CONFIG.SiteApiUrl=站點API地址

# 錯誤資訊
ERRCODE.CMS.CONTENTCORE.NO_SITE=無站點數據
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_PAGE_WIDGET_TYPE=未知頁面部件類型：{0}
ERRCODE.CMS.CONTENTCORE.TEMPLATE_EMPTY=模板未配置或模板檔案不存在
ERRCODE.CMS.CONTENTCORE.TEMPLATE_FILE_NOT_FOUND=模板檔案“{0}”不存在
ERRCODE.CMS.CONTENTCORE.INVALID_TEMPLATE_NAME=模板檔案名只能使用大小寫字母和下劃線，且必須以`{0}`結尾
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CONTENT_TYPE=未知內容類型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_CATALOG_TYPE=未知欄目類型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_INTERNAL_DATA_TYPE=未知內部數據類型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_RESOURCE_TYPE=未知資源類型：{0}
ERRCODE.CMS.CONTENTCORE.UNSUPPORTED_DYNAMIC_PAGE_TYPE=未知動態模板類型：{0}
ERRCODE.CMS.CONTENTCORE.DEL_CHILD_FIRST=請先刪除子欄目
ERRCODE.CMS.CONTENTCORE.CONFLICT_CATALOG=欄目名稱/別名/目錄重複
ERRCODE.CMS.CONTENTCORE.CATALOG_MAX_TREE_LEVEL=欄目層級超出上限
ERRCODE.CMS.CONTENTCORE.CATALOG_MOVE_TO_SELF_OR_CHILD=欄目不能轉移到自身或子欄目
ERRCODE.CMS.CONTENTCORE.INVALID_PROPERTY=擴展屬性[{0}={1}]校驗失敗
ERRCODE.CMS.CONTENTCORE.EXISTS_SITE_PATH=站點目錄衝突
ERRCODE.CMS.CONTENTCORE.CONTENT_LOCKED=內容已被"{0}"鎖定
ERRCODE.CMS.CONTENTCORE.TITLE_REPLEAT=標題重複
ERRCODE.CMS.CONTENTCORE.CANNOT_EDIT_PUBLISHED_CONTENT=已發布內容不允許編輯，請先下線內容！
ERRCODE.CMS.CONTENTCORE.NO_PUBLISHPIPE=無可用發布通道
ERRCODE.CMS.CONTENTCORE.CATALOG_CANNOT_PUBLISH=欄目發布失敗：欄目不可見/不可靜態化/標題欄目。
ERRCODE.CMS.CONTENTCORE.SITE_FILE_OP_ERR=不能操作非當前站點檔案
ERRCODE.CMS.CONTENTCORE.TEMPLATE_PATH_EXISTS=模板檔案已存在或路徑被佔用
ERRCODE.CMS.CONTENTCORE.NOT_ALLOW_FILE_TYPE=禁止上傳的檔案類型：{0}
ERRCODE.CMS.CONTENTCORE.NOT_EDITABLE_FILE=指定檔案不支援線上編輯
ERRCODE.CMS.CONTENTCORE.FILE_NOT_EXIST=檔案不存在
ERRCODE.CMS.CONTENTCORE.FILE_ALREADY_EXISTS=檔案已存在
ERRCODE.CMS.CONTENTCORE.CATALOG_SORT_VALUE_ZERO=欄目排序值不能為0
ERRCODE.CMS.CONTENTCORE.SITE_EXPORT_TASK_EXISTS=站點導出任務正在進行中
ERRCODE.CMS.CONTENTCORE.DEL_CONTENT_ERR=只能刪除初稿和已下線內容
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_NO_PASSED=存在需要審批且未通過的内容，不可操作
ERRCODE.CMS.CONTENTCORE.CONTAIN_CONTENT_IN_FLOW=存在進入審批流程的内容，不可操作

# freemarker模板標籤
FREEMARKER.TAG.NAME.cms_site=站點列表標籤
FREEMARKER.TAG.DESC.cms_site=獲取站點數據列表，內嵌<#list DataList as site>${catalog.site}</#list>遍曆數據
FREEMARKER.TAG.NAME.cms_catalog=欄目列表標籤
FREEMARKER.TAG.DESC.cms_catalog=獲取欄目數據列表，內嵌<#list DataList as catalog>${catalog.name}</#list>遍曆數據
FREEMARKER.TAG.NAME.cms_content=內容列表標籤
FREEMARKER.TAG.DESC.cms_content=獲取內容數據列表，內嵌<#list DataList as content>${content.name}</#list>遍曆數據
FREEMARKER.TAG.NAME.cms_site_property=站點自定義屬性標籤
FREEMARKER.TAG.DESC.cms_site_property=獲取站點自定義屬性數據列表，內嵌<#list DataList as prop>${prop.propName}</#list>遍曆數據
FREEMARKER.TAG.NAME.cms_include=模板引用標籤
FREEMARKER.TAG.DESC.cms_include=引用其他模板內容，支援ssi引用標籤
FREEMARKER.TAG.NAME.cms_pagewidget=頁面部件引用標籤
FREEMARKER.TAG.DESC.cms_pagewidget=引用頁面部件內容，支援ssi引用標籤
FREEMARKER.TAG.NAME.cms_pagewidget_data=頁面部件數據標籤
FREEMARKER.TAG.DESC.cms_pagewidget_data=頁面部件數據標籤，標籤體內可使用${Data.name}獲取數據
FREEMARKER.TAG.NAME.cms_content_closest=內容前後篇標籤
FREEMARKER.TAG.DESC.cms_content_closest=內容前後篇標籤，僅支援指定內容當前欄目列表，標籤體內可使用${Data.title}獲取數據
FREEMARKER.TAG.NAME.cms_content_rela=相關內容標籤
FREEMARKER.TAG.DESC.cms_content_rela=相關內容標籤，內嵌<#list DataList as content>${content.name}</#list>遍曆數據

# freemarker模板函數
FREEMARKER.FUNC.DESC.clearHtmlTag=清除Html標籤，例如：${clearHtmlTag(ArticleContent)}
FREEMARKER.FUNC.DESC.dateFormat=日期格式化，例如：${dateFormat(content.publishDate)}
FREEMARKER.FUNC.DESC.htmlInternalUrl=將html文本中的內部連結地址“iurl://”解析為正常http(s)訪問地址，例如：${htmlInternalUrl(ArticleContent)}
FREEMARKER.FUNC.DESC.imageSize=獲得圖片縮放圖函數，不在的縮略圖會自動建立，例如：${imageSize(content.logo, 300, 300)}
FREEMARKER.FUNC.DESC.internalUrl=將內部連結“iurl://”解析為正常http(s)訪問地址，例如：${internalUrl(content.redirectUrl)}
FREEMARKER.FUNC.DESC.randomInt=獲取指定範圍隨機數，例如：${randomInt(10,100)}
FREEMARKER.FUNC.DESC.siteUrl=獲取站點指定發布通道訪問連結，例如：${siteUrl(Site.siteId, 'h5')}
FREEMARKER.FUNC.DESC.catalogUrl=獲取欄目指定發布通道訪問連結，例如：${catalogUrl(Catalog.catalogId, 'h5')}
FREEMARKER.FUNC.DESC.contentUrl=獲取內容指定發布通道訪問連結，例如：${contnetUrl(Content.contentId, 'h5')}
FREEMARKER.FUNC.DESC.ifElse=三元表達式函數，例如：${ifElse(content.logo??, 'x1', 'x2')}
FREEMARKER.FUNC.DESC.dynamicPageLink=動態頁面連結獲取函數，例如：${dynamicPageLink('Search')}
FREEMARKER.FUNC.DESC.dict=獲取字典數據列表，例如：${dict('YesOrNo', 'Y')}
FREEMARKER.FUNC.DESC.sysConfig=獲取系統參數配置值，例如：${sysConfig('SiteApiUrl')}
FREEMARKER.FUNC.DESC.listHtmlInternalUrl=獲取html文本中的iurl列表，例如：${listHtmlInternalUrl(ArticleContent)}
FREEMARKER.FUNC.DESC.contentPageLink=獲取內容分頁鏈接，例如：${contentPageLink(content.link, 2)}
FREEMARKER.FUNC.DESC.videoPlayer=將html文本中的視頻資源連結替換為<video>視頻播放器
FREEMARKER.FUNC.videoPlayer.Arg1.Name=Html文本內容
FREEMARKER.FUNC.videoPlayer.Arg2.Name=視頻寬度
FREEMARKER.FUNC.videoPlayer.Arg2.Desc=預設：100%
FREEMARKER.FUNC.videoPlayer.Arg3.Name=視頻高度

# 校驗規則
VALIDATOR.CMS.SITE.PUBLISH_PIPE_PROPS_EMPTY=發布通道配置不能為空

# 定時任務
SCHEDULED_TASK.RecycleExpireJobHandler=資源回收筒過期內容刪除任務
SCHEDULED_TASK.SitePublishJobHandler=定時發布任務
SCHEDULED_TASK.ContentTopCancelJobHandler=內容置頂取消任務
SCHEDULED_TASK.UpdateDynamicDataJobHandler=保存內容動態數據任務
SCHEDULED_TASK.ContentOfflineJobHandler=內容定時下線任務
