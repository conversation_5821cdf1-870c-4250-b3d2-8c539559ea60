package com.chestnut.contentcore.controller;

import com.chestnut.common.domain.R;
import com.chestnut.common.log.annotation.Log;
import com.chestnut.common.log.enums.BusinessType;
import com.chestnut.common.security.anno.Priv;
import com.chestnut.contentcore.openapi.entity.vo.FlowWithdrawVo;
import com.chestnut.contentcore.openapi.service.FlowService;
import com.chestnut.contentcore.util.CmsPrivUtils;
import com.chestnut.system.security.AdminUserType;
import com.chestnut.system.validator.LongId;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 内容流程管理控制器
 *
 * <AUTHOR>
 */
@Priv(type = AdminUserType.TYPE, value = CmsPrivUtils.PRIV_SITE_VIEW_PLACEHOLDER)
@RequiredArgsConstructor
@RestController
@RequestMapping("/cms/content/flow")
public class ContentFlowController {

    private final FlowService flowService;

    @Log(title = "发起审核", businessType = BusinessType.FLOW)
    @PostMapping("/start/{catalogId}/{contentType}/{contentId}")
    public R<?> start(@PathVariable("catalogId") @LongId Long catalogId, @PathVariable("contentType") String contentType, @PathVariable("contentId") Long contentId) {
        flowService.startFlow(catalogId, contentId, contentType);
        return R.ok();
    }

    @Log(title = "重新发起审核", businessType = BusinessType.FLOW)
    @PostMapping("/restart/{catalogId}/{contentType}/{contentId}")
    public R<?> restart(@PathVariable("catalogId") @LongId Long catalogId, @PathVariable("contentType") String contentType, @PathVariable("contentId") Long contentId) {
        flowService.restartFlow(catalogId, contentId, contentType);
        return R.ok();
    }

    @Log(title = "撤回流程", businessType = BusinessType.FLOW)
    @PostMapping("/withdraw/{catalogId}/{contentType}/{contentId}")
    public R<?> revoke(@PathVariable("catalogId") @LongId Long catalogId, @PathVariable("contentType") String contentType, @PathVariable("contentId") Long contentId,
                       @RequestBody FlowWithdrawVo vo) {
        flowService.withdrawFlow(catalogId, contentId, contentType,vo);
        return R.ok();
    }

    @Log(title = "流程进度", businessType = BusinessType.FLOW)
    @GetMapping("/progress/{catalogId}/{contentType}/{contentId}")
    public R<Map<String, Object>> progress(@PathVariable("catalogId") @LongId Long catalogId, @PathVariable("contentType") String contentType, @PathVariable("contentId") Long contentId) {
        Map<String, Object> progress = flowService.flowProgress(catalogId, contentId, contentType);
        return R.ok(progress);
    }

    @Log(title = "流程表单", businessType = BusinessType.FLOW)
    @GetMapping("/form/{catalogId}/{contentType}/{contentId}")
    public R<Map<String, Object>> form(@PathVariable("catalogId") @LongId Long catalogId, @PathVariable("contentType") String contentType, @PathVariable("contentId") Long contentId) {
        Map<String, Object> form = flowService.flowForm(catalogId, contentId, contentType);
        return R.ok(form);
    }

    @Log(title = "流程表单", businessType = BusinessType.FLOW)
    @GetMapping("/log/{dataId}/{version}")
    public R<Map<String, Object>> log(@PathVariable("dataId") @LongId String dataId, @PathVariable("version") String version) {
        Map<String, Object> data = flowService.log(dataId, version);
        return R.ok(data);
    }


}
