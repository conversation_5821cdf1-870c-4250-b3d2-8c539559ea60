package com.chestnut.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.chestnut.common.annotation.RelationGlobalDict;
import com.chestnut.common.core.KeyValue;
import com.chestnut.common.domain.dto.DictDataRespDTO;
import com.chestnut.common.domain.dto.SelectDictRespDTO;
import com.chestnut.common.utils.cache.CacheUtils;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toSet;

/**
 * 字典工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DictFrameworkUtils {

    private static DictDataApi dictDataApi;

    private static final DictDataRespDTO DICT_DATA_NULL = new DictDataRespDTO();

    /**
     * 针对 {@link #getDictDataLabel(String, String)} 的缓存
     */
    private static final LoadingCache<KeyValue<String, String>, DictDataRespDTO> GET_DICT_DATA_CACHE = CacheUtils
            .buildAsyncReloadingCache(Duration.ofMinutes(1L), // 过期时间 1 分钟
                    new CacheLoader<KeyValue<String, String>, DictDataRespDTO>() {

                        @Override
                        public DictDataRespDTO load(KeyValue<String, String> key) {
                            return ObjectUtil.defaultIfNull(dictDataApi.getDictData(key.getKey(), key.getValue()),
                                    DICT_DATA_NULL);
                        }

                    });

    /**
     * 针对 {@link #parseDictDataValue(String, String)} 的缓存
     */
    private static final LoadingCache<KeyValue<String, String>, DictDataRespDTO> PARSE_DICT_DATA_CACHE = CacheUtils
            .buildAsyncReloadingCache(Duration.ofMinutes(1L), // 过期时间 1 分钟
                    new CacheLoader<KeyValue<String, String>, DictDataRespDTO>() {

                        @Override
                        public DictDataRespDTO load(KeyValue<String, String> key) {
                            return ObjectUtil.defaultIfNull(dictDataApi.parseDictData(key.getKey(), key.getValue()),
                                    DICT_DATA_NULL);
                        }

                    });

    public static void init(DictDataApi dictDataApi) {
        DictFrameworkUtils.dictDataApi = dictDataApi;
        log.info("[init][初始化 DictFrameworkUtils 成功]");
    }

    @SneakyThrows
    public static String getDictDataLabel(String dictType, String value) {
        return GET_DICT_DATA_CACHE.get(new KeyValue<>(dictType, value)).getLabel();
    }

    @SneakyThrows
    public static String parseDictDataValue(String dictType, String label) {
        return PARSE_DICT_DATA_CACHE.get(new KeyValue<>(dictType, label)).getValue();
    }

    /**
     * 用户构建带有分页信息的数据列表。
     * @param dataList 数据列表，该参数必须是调用PageMethod.startPage之后，立即执行mybatis查询操作的结果集。
     * @return 返回分页数据对象。
     */
    public static <T> void makeResponseData(List<T> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        Field[] fields = ReflectUtil.getFields(dataList.get(0).getClass());
        for (Field f : fields) {
            RelationGlobalDict relationGlobalDict = f.getAnnotation(RelationGlobalDict.class);
            if (relationGlobalDict != null) {
                Set<Serializable> masterIdSet = dataList.stream()
                        .map(obj -> ReflectUtil.getFieldValue(obj, relationGlobalDict.masterIdField()))
                        .filter(Objects::nonNull).map(x -> x.toString()).collect(toSet());
                List<SelectDictRespDTO> dictRespDTOS = dictDataApi
                        .getGlobalSelectDictListFromCache(relationGlobalDict.dictCode().name(), masterIdSet);
                Map<Object, String> dictMap = dictRespDTOS.stream()
                        .collect(Collectors.toMap(x -> x.getValue().toString(), x -> x.getLabel()));
                dataList.forEach(data -> {
                    Object masterValue = ReflectUtil.getFieldValue(data, relationGlobalDict.masterIdField());
                    if (masterValue != null) {
                        String value = dictMap.get(masterValue.toString());
                        ReflectUtil.setFieldValue(data, f, value);
                    }
                });
            }
        }

    }

}
