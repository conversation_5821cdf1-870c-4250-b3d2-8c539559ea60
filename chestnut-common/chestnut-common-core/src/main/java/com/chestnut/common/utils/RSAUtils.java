package com.chestnut.common.utils;

import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;

/**
 * 
 */
public class RSAUtils {

    public static final String DEFAULT_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNcRNGaRRWgxIA0R9dGcbnaSddYWZTy1rHN0LGK1stMmKWN7Rp3D4LRXKdX7OCO3QYTPWNnxAu2L4Vz4NpytMdAHVI5Mq5ls2sJbFGDZ8SNyXiVAZ/dnZ2178CJE9ZgqgN2ZS/M7/LYDUT4SASh8T1jBCVmKYstkuk7pixqdKSFwIDAQAB";

    public static final String DEFAULT_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAI1xE0ZpFFaDEgDRH10ZxudpJ11hZlPLWsc3QsYrWy0yYpY3tGncPgtFcp1fs4I7dBhM9Y2fEC7YvhXPg2nK0x0AdUjkyrmWzawlsUYNnxI3JeJUBn92dnbXvwIkT1mCqA3ZlL8zv8tgNRPhIBKHxPWMEJWYpiy2S6TumLGp0pIXAgMBAAECgYAE0S0O4NVIfP5H+NdQnGghyXsFuyN5Bxv4XPFxPljEmUqyQwNp945FZAAbS/8ZGF1D1psmT5yFe+w9Yevf1oCKFtoiYKZUZfCw4K2otZdH/wA0nEFPDF3d0XDd4oc2afJAej60kO4aVwcO9Sge2RNeccJX14eatLGnTxnYPQA+gQJBAPtugNNQS/W1aeKmlCAwLVjZsLARs7wZ8xmM+zzxstyArUdV4M4QOGCdJ4pZnE8e8c8eJ9zrVbECyFack2yjjr8CQQCQAvhhOZ6wHBCOcyNVHmzdW2DXlBKlnH5RKALYGaSuVeUaI3psuF/xq2xQdUHIc5qMOdCdg4eeBIDAOautEiqpAkAYoVaVXMRUKZVZ0p+oF5AZqBma5eEJiDE8S4dBDRxMZX6UAmGnTgd3z21ULDsXtRxTAc094v/d+zvM4EHA3rTZAkAr4CvKe2KkpzhIHj4yjb1/R3re02dG+8Hkefs72bw5vUO6zfpbq2TX3XF3pfHv34DDnk23373u39JWPnvWlAVZAkEAqdE9pC+9liDMRHQuegTyxBEl7Rosi3Byi2zpWHjtrJgHt2mbhyuh/7cnMc3lVFYejNbVBq5kkwF0CtULCIHN7w==";

    /**
     * 随机生成密钥对
     * @throws NoSuchAlgorithmException
     */
    public static HashMap<String, String> genKeyPair() throws NoSuchAlgorithmException {
        HashMap<String, String> keyMap = Maps.newHashMap();
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024, new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        // 得到公钥和私钥字符串
        String publicKeyStr = Base64.encodeBase64String(publicKey.getEncoded());
        String privateKeyStr = Base64.encodeBase64String(privateKey.getEncoded());
        keyMap.put("publicKey", publicKeyStr);
        keyMap.put("privateKey", privateKeyStr);
        return keyMap;
    }

    /**
     * RSA公钥加密
     * @param str 加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws Exception 加密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        // base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        RSAPublicKey RSAKey = (RSAPublicKey) keyFactory.generatePublic(new X509EncodedKeySpec(decoded));
        // RSA加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, RSAKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes("UTF-8")));
        return outStr;
    }

    /**
     * RSA私钥解密
     * @param str 加密字符串
     * @param privateKey 私钥
     * @return 明文
     * @throws Exception 解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) {
        try {
            // 64位解码加密后的字符串
            byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
            // base64编码的私钥
            byte[] decoded = Base64.decodeBase64(privateKey);
            // 构造PKCS8EncodedKeySpec对象
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            RSAPrivateKey RSAKey = (RSAPrivateKey) keyFactory.generatePrivate(pkcs8KeySpec);
            // RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, RSAKey);
            String outStr = new String(cipher.doFinal(inputByte));
            return outStr;
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        // 生成公钥和私钥
        HashMap<String, String> keyMap = genKeyPair();
        // 加密字符串
        String message = "123456";
        String publicKey = keyMap.get("publicKey");
        String privateKey = keyMap.get("privateKey");
        System.out.println("随机生成的公钥为:" + publicKey);
        System.out.println("随机生成的私钥为:" + privateKey);
        String messageEn = encrypt(message, publicKey);
        System.out.println(message + "\t加密后的字符串为:" + messageEn);
        String messageDe = decrypt(messageEn, privateKey);
        System.out.println("还原后的字符串为:" + messageDe);
    }

}
