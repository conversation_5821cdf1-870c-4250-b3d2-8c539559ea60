package com.chestnut.common.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectDictRespDTO {

	/**
	 * 文本
	 */
	private String label;

	/**
	 * 编码
	 */
	private Object value;

	/**
	 * 排序
	 */
	private Integer sort;

	/**
	 * 父字段
	 */
	private Object parentValue;

	/**
	 * 备用字段1
	 */
	private Object extra1;

	/**
	 * 备用字段2
	 */
	private Object extra2;

	/**
	 * 备用字段3
	 */
	private Object extra3;
	
}
