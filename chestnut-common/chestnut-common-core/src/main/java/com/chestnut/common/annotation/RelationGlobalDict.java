package com.chestnut.common.annotation;


import com.chestnut.common.enums.GlobalDictCodeEnum;

import java.lang.annotation.*;

/**
 * 全局常量字典
 *
 * <AUTHOR>
 * @date 2022/5/16
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RelationGlobalDict {

	/**
	 * 当前对象的关联Id字段名称。
	 *
	 * @return 当前对象的关联Id字段名称。
	 */
	String masterIdField();

	/**
	 * 全局字典编码。
	 *
	 * @return 全局字典编码。空表示为不使用全局字典。
	 */
	GlobalDictCodeEnum dictCode();
}
