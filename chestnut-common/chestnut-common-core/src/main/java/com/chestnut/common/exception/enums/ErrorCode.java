package com.chestnut.common.exception.enums;

import lombok.Data;

/**
 * 错误码对象
 * TODO 错误码设计成对象的原因，为未来的 i18 国际化做准备
 */
@Data
public class ErrorCode {

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误提示
     */
    private String msg;

    public ErrorCode(Integer code, String message) {
        this.code = code;
        this.msg = message;
    }

    public ErrorCode format(Object... args) {
        this.setMsg(String.format(this.msg, args));
        return this;
    }

}
