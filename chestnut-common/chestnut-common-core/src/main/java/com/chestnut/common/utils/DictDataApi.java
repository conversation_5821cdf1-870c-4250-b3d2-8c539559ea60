package com.chestnut.common.utils;


import com.chestnut.common.domain.dto.DictDataRespDTO;
import com.chestnut.common.domain.dto.SelectDictRespDTO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 字典数据 API 接口
 *
 * <AUTHOR>
 */
public interface DictDataApi {

    /**
     * 校验字典数据们是否有效。如下情况，视为无效： 1. 字典数据不存在 2. 字典数据被禁用
     * @param dictType 字典类型
     * @param values 字典数据值的数组
     */
    void validDictData(String dictType, Collection<String> values);

    /**
     * 获得指定的字典数据，从缓存中
     * @param type 字典类型
     * @param value 字典数据值
     * @return 字典数据
     */
    DictDataRespDTO getDictData(String type, String value);

    /**
     * 解析获得指定的字典数据，从缓存中
     * @param type 字典类型
     * @param label 字典数据标签
     * @return 字典数据
     */
    DictDataRespDTO parseDictData(String type, String label);

    /**
     * 从缓存中获取指定编码的字典项目列表。 该方法通常会在业务主表中调用，为了提升整体运行时效率，该方法会从缓存中获取，如果缓存为空，
     * 会从数据库读取指定编码的字典数据，并同步到缓存。
     * @param dictType 字典编码。
     * @param itemIds 字典项目Id集合。
     * @return 查询结果列表。
     */
    List<SelectDictRespDTO> getGlobalSelectDictListFromCache(String dictType, Set<Serializable> itemIds);

    /**
     * 从缓存中获取指定编码的字典项目列表。返回的结果Map中，键是itemId，值是itemName。
     * 该方法通常会在业务主表中调用，为了提升整体运行时效率，该方法会从缓存中获取，如果缓存为空， 会从数据库读取指定编码的字典数据，并同步到缓存。
     * @param dictType 字典编码。
     * @param itemIds 字典项目Id集合。
     * @return 查询结果列表。
     */
    Map<Object, String> getGlobalSelectDictMapFromCache(String dictType, Set<Serializable> itemIds);

    SelectDictRespDTO getGlobalSelectDictFromCache(String dictType, String itemId);

}
