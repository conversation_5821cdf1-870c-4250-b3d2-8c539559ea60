#错误消息
ERRCODE.COMMON.NOT_NULL=Parameter "{0}" cannot be null.
ERRCODE.COMMON.NOT_EMPTY=Parameter "{0}" cannot be empty.
ERRCODE.COMMON.DATA_NOT_FOUND=Data not found.
ERRCODE.COMMON.DATA_NOT_FOUND_BY_ID=Data not found: {0}={1}
ERRCODE.COMMON.DATA_CONFLICT=Data conflict: [{0}]
ERRCODE.COMMON.INVALID_REQUEST_ARG=Invalid parameter: {0}
ERRCODE.COMMON.INVALID_REQUEST_METHOD=Invalid request method.
ERRCODE.COMMON.SYSTEM_ERROR=System error.
ERRCODE.COMMON.UNKNOWN_ERROR=Unknown error.
ERRCODE.COMMON.DATABASE_FAIL=Database error.
ERRCODE.COMMON.REQUEST_FAILED=Http(s) request failed: {0}
ERRCODE.COMMON.FIXED_DICT=The fixed dict cannot be delete or modify type.
ERRCODE.COMMON.FIXED_DICT_NOT_ALLOW_ADD=The fixed dict not allow to add data items.
ERRCODE.COMMON.FIXED_CONFIG_DEL=The fixed config "{0}" cannot be delete.
ERRCODE.COMMON.FIXED_CONFIG_UPDATE=The fixed config "{0}" cannot modify key.
ERRCODE.COMMON.ASYNC_TASK_RUNNING=The task "{0}" is running.