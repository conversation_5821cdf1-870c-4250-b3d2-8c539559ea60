#错误消息
ERRCODE.COMMON.NOT_NULL=参数[{0}]不能为NULL
ERRCODE.COMMON.NOT_EMPTY=参数[{0}]不能为空
ERRCODE.COMMON.DATA_NOT_FOUND=数据不存在
ERRCODE.COMMON.DATA_NOT_FOUND_BY_ID=数据不存在：{0}={1}
ERRCODE.COMMON.DATA_CONFLICT=数据[{0}]冲突
ERRCODE.COMMON.INVALID_REQUEST_ARG=请求参数[{0}]不符合校验规则
ERRCODE.COMMON.INVALID_REQUEST_METHOD=请求方法错误
ERRCODE.COMMON.SYSTEM_ERROR=系统内部错误
ERRCODE.COMMON.UNKNOWN_ERROR=未知错误
ERRCODE.COMMON.DATABASE_FAIL=数据库操作失败
ERRCODE.COMMON.REQUEST_FAILED=Http(s)请求失败：{0}
ERRCODE.COMMON.FIXED_DICT=系统固定字典数据不允许删除或修改类型
ERRCODE.COMMON.FIXED_DICT_NOT_ALLOW_ADD=此系统固定字典类型不能添加子项
ERRCODE.COMMON.FIXED_CONFIG_DEL=系统固定配置参数[{0}]不能删除
ERRCODE.COMMON.FIXED_CONFIG_UPDATE=系统固定配置参数[{0}]不能修改键名
ERRCODE.COMMON.ASYNC_TASK_RUNNING=任务“{0}”正在运行中